<?php

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdController;
use App\Http\Controllers\OtpController;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BPayController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\BadgeController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\OfferController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\PromoCodeController;
use App\Http\Controllers\DeviceTokenController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PaymentProofController;
use App\Http\Controllers\DeliveryChargeController;
use App\Http\Controllers\EPaymentProviderController;
use App\Http\Controllers\PaymentVerificationController;
use App\Http\Controllers\StorePaymentProviderController;
use App\Http\Controllers\StorePaymentVerificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/



Route::post('test-bpay', [BPayController::class, 'testBpay']);


Route::middleware('auth:api')->group(function () {
    Route::get('/notifications', [NotificationController::class, 'getUserNotifications']);
    Route::put('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead']);
    Route::put('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead']);
    Route::post('/notifications/send', [NotificationController::class, 'sendPushNotification']);
});



Route::middleware('auth:api')->get('/badges', [BadgeController::class, 'getCounts']);
Route::middleware('auth:api')->post('/badges/reset', [BadgeController::class, 'reset']);

Route::middleware('auth:sanctum')->group(function () {
    Route::get('stores/payment-providers', [StorePaymentProviderController::class, 'getStoreProviders']);
    Route::get('stores/{storeId}/payment-providers', [StorePaymentProviderController::class, 'getStoreProvidersById']);
    Route::post('stores/payment-providers', [StorePaymentProviderController::class, 'updateStoreProviders']);
    Route::get('stores/{store_id}/payment-providers/{provider_id}', [StorePaymentProviderController::class, 'getStoreProviderDetails']);
});


Route::middleware('auth:sanctum')->post('stores/payment-providers/add', [StorePaymentProviderController::class, 'addProvider']);

Route::middleware(['auth:sanctum'])->prefix('admin')->group(function () {
    Route::get('payment-verifications/stats', [PaymentVerificationController::class, 'stats']);
    Route::apiResource('payment-verifications', PaymentVerificationController::class)
        ->only(['index', 'show', 'update']);
});
Route::middleware('auth:sanctum')->group(function () {


    Route::prefix('payment-providers')->group(function () {
        Route::get('/', [EPaymentProviderController::class, 'index']);
        Route::get('/api-enabled', [EPaymentProviderController::class, 'getApiEnabled']);
        Route::get('/{id}', [EPaymentProviderController::class, 'show']);
    });

    Route::post('payment-proofs', [PaymentProofController::class, 'store']);
    Route::get('payment-proofs/{id}', [PaymentProofController::class, 'show']);

    Route::get('category-prices', [PaymentProofController::class, 'getCategoryPrices']);
    Route::post('calculate-price', [PaymentProofController::class, 'calculatePrice']);

    Route::prefix('store/payments')->middleware('has.store')->group(function () {
        Route::get('/', [StorePaymentVerificationController::class, 'index']);
        Route::post('{id}/verify', [StorePaymentVerificationController::class, 'verify']);
    });
    Route::put('/user/update-language', [AuthController::class, 'updateLanguage']);
    Route::get('user/orders', [ProfileController::class, 'getUserOrders']);
    Route::put('user/profile', [ProfileController::class, 'updateProfile']);
    Route::put('user/phone', [ProfileController::class, 'updatePhone']);
    Route::get('user/leader-board', [ProfileController::class, 'getLeaderBoard']);


    Route::get('/user/discussions', [OfferController::class, 'getUserDiscussions']);

});

Route::group([
    'middleware' => 'api',
    'namespace' => 'App\Http\Controllers'
], function ($router) {
    Route::post('auth/login', ['as' => 'login', 'uses' => 'AuthController@login']);
    Route::post('auth/register', ['as' => 'register', 'uses' => 'AuthController@register']);
    Route::post('auth/logout', 'AuthController@logout');
    Route::middleware('auth:api')->post('change-password', [AuthController::class, 'changePassword']);
    Route::middleware('auth:api')->post('password-recovery', [AuthController::class, 'recoverPassword']);
    Route::middleware('auth:api')->post('auth/request-delete-account', [AuthController::class, 'requestDeleteAccount']);
    Route::middleware('auth:api')->delete('auth/delete-account', [AuthController::class, 'deleteAccount']);
    Route::middleware('auth:api')->post('auth/recover-account', [AuthController::class, 'recoverAccount']);
    Route::post('auth/verify-otp', [AuthController::class, 'verifyOtpAndSetVerified']);
});

Route::get('/stores/favorites-categories', [StoreController::class, 'getFavoriteCategories'])->middleware('auth:api');
Route::middleware('auth:api')->post('/stores/add-favorites-category', [StoreController::class, 'addFavoriteCategory']);
Route::get('stores/promoted-recommended', [StoreController::class, 'getRecommendedPromotedStores']);
Route::get('stores/general-recommended', [StoreController::class, 'getGeneralRecommendedStores']);

Route::middleware('auth:api')->post('items/like-unlike/{item}', [ItemController::class, 'likeUnlike']);
Route::middleware('auth:api')->post('items/add-payment', [ItemController::class, 'addPayment']);

Route::middleware('auth:api')->get('/liked-items', [ItemController::class, 'getLikedItems']);
Route::middleware('auth:api')->delete('/stores/favorite-category/{categoryId}', [StoreController::class, 'deleteFavoriteCategory']);
Route::middleware('auth:api')->get('items/recommended', [ItemController::class, 'getRecommendedItems']);
Route::get('items/general-recommended', [ItemController::class, 'getGeneralRecommendedItems']);
Route::middleware('auth:api')->post('items', [ItemController::class, 'store']);

Route::put('/items-status/{item}', [ItemController::class, 'updateStatus']);
Route::middleware('auth:api')->get('items/user-items-status', [ItemController::class, 'getUserItemsStatuses']);
Route::middleware('auth:api')->post('items-update', [ItemController::class, 'updateItem']);
Route::middleware('auth:api')->delete('items/{id}/delete', [ItemController::class, 'destroyItem']);
Route::middleware('auth:api')->get('items/mine', [ItemController::class, 'getUserItems']);

Route::get('categories', [CategoryController::class, 'getAll']);
Route::post('/categories', [CategoryController::class, 'store']);

Route::get('brands', [BrandController::class, 'index']);
Route::post('brands', [BrandController::class, 'store']);


Route::middleware('auth:api')->get('followed-stores', [ItemController::class, 'getFollowedStores']);

Route::middleware('auth:api')->get('items/search', [ItemController::class, 'search']);


Route::post('promo-codes/calculate-item-price', [PromoCodeController::class, 'calculateItemPrice'])->name('promo-codes.calculateItemPrice');

Route::get('items-public-search', [ItemController::class, 'publicSearch']);


Route::middleware('auth:api')->get('search-history', [ItemController::class, 'getSearchHistory']);
Route::middleware('auth:api')->delete('search-history/{id}', [ItemController::class, 'deleteSearchHistory']);

Route::get('popular-categories', [ItemController::class, 'mostSearchedCategories']);



Route::get('/stores/{storeId}/items', [StoreController::class, 'getItemsByStoreId']);




Route::get('items/{id}', [ItemController::class, 'getItem']);




Route::get('/stores/{storeId}/favorites-categories', [StoreController::class, 'getFavoriteCategoriesByStoreId']);

Route::middleware('auth:sanctum')->group(function () {



    Route::get('order-store', [StoreController::class, 'getStoreOrders']);

    Route::post('payments/bpay/initialize', [BPayController::class, 'initialize']);

    Route::post('payments/bpay/process', [BPayController::class, 'process']);

    Route::post('offers', [OfferController::class, 'store']);


    Route::post('discussions', [OfferController::class, 'createDiscussion']);

    Route::get('discussions/{itemID}', [OfferController::class, 'getMessages']);
    Route::post('discussions/{itemID}/messages', [OfferController::class, 'sendMessage']);
    Route::post('/stores', [StoreController::class, 'createStore']);
    Route::post('update-store', [StoreController::class, 'updateStore']);
    Route::get('stores/recommended', [StoreController::class, 'getRecommendedStores']);
    Route::put('/stores/location', [StoreController::class, 'updateStoreLocation']);
    Route::get('/store-liked-items/{storeId}', [StoreController::class, 'getLikedItemsForStore']);
    Route::put('/stores/follow/{storeId}', [StoreController::class, 'followStore']);
    Route::get('/followed-stores', [StoreController::class, 'getFollowedStoresByUser']);
    Route::middleware('auth:api')->get('/stores/info', [StoreController::class, 'getStoreInfo']);
    Route::middleware('auth:api')->get('/stores/{storeId}/items/favorite-category/{categoryId}', [StoreController::class, 'getItemsByFavoriteCategoryId']);
    Route::middleware('auth:api')->get('/stores/{storeId}/items/category/{categoryId}', [StoreController::class, 'getItemsByCategoryId']);
    Route::middleware('auth:api')->get('/stores/get-my-store-items', [StoreController::class, 'getMyStoreItems']);
    Route::middleware('auth:api')->post('/stores/promote', [StoreController::class, 'promoteStore'])->name('stores.promote');
    Route::middleware('auth:api')->post('/store-items', [StoreController::class, 'storeItem']);
    Route::middleware('auth:api')->post('/store/item/{itemId}/promotion', [StoreController::class, 'setPromotionForItemInMyStore']);
    Route::middleware('auth:api')->post('/stores/my-store/promotions', [StoreController::class, 'addPromotionForMyStore']);
    Route::middleware('auth:api')->delete('/stores/my-store/promotions', [StoreController::class, 'removePromotionForMyStore']);
    Route::middleware('auth:api')->delete('/store/item/{itemId}/promotion', [StoreController::class, 'removePromotionForItemInMyStore']);
    Route::middleware('auth:api')->get('/stores/my-store/location', [StoreController::class, 'getStoreLocation']);
    Route::middleware('auth:api')->post('/stores/my-store/location', [StoreController::class, 'postStoreLocation']);
    Route::middleware('auth:api')->put('/stores/my-store/location/edit', [StoreController::class, 'editStoreLocation']);

    Route::middleware('auth:api')->post('/stores/my-store/location', [StoreController::class, 'updateStoreLocation']);
    Route::delete('/stores/my-store/items/{itemId}', [StoreController::class, 'deleteItemFromMyStore']);
    Route::middleware('auth:api')->put('/stores/my-store/items/{itemId}', [ItemController::class, 'updateItemInMyStore']);

    Route::middleware('auth:api')->post('/users/location', [UserController::class, 'storeUserLocation']);
    Route::middleware('auth:api')->get('/users/location', [UserController::class, 'getUserLocation']);
    Route::middleware('auth:api')->put('/users/location', [UserController::class, 'updateUserLocation']);
});

// Place the general {id} route after all specific store routes to avoid conflicts
Route::get('/stores/{id}', [StoreController::class, 'getStore']);

Route::get('/store-types', [StoreController::class, 'getStoreTypes']);


Route::middleware('auth:api')->post('device-tokens', [DeviceTokenController::class, 'store']);
Route::middleware('auth:api')->get('device-tokens/current', [DeviceTokenController::class, 'getTokenByCurrentUser']);
Route::middleware('auth:api')->delete('device-tokens', [DeviceTokenController::class, 'destroy']);



Route::middleware('auth:api')->post('payment-order', [OrderController::class, 'orderPayment']);
Route::middleware('auth:api')->post('orders', [OrderController::class, 'createOrder']);
Route::middleware('auth:api')->put('orders-update-status', [OrderController::class, 'updateOrderStatus']);
Route::middleware('auth:api')->put('orders/{id}', [OrderController::class, 'updateOrder']);
Route::middleware('auth:api')->get('orders/mine', [OrderController::class, 'getUserOrders']);
Route::middleware('auth:api')->post('orders/new', [OrderController::class, 'createOrderTest']);
Route::middleware('auth:api')->put('/orders/{id}/edit', [OrderController::class, 'editUserOrder']);
Route::get('delivery-charges', [DeliveryChargeController::class, 'index']);
Route::middleware('auth:api')->post('delivery-charges', [DeliveryChargeController::class, 'store']);
Route::middleware('auth:api')->get('delivery-charges/{id}', [DeliveryChargeController::class, 'show']);
Route::middleware('auth:api')->put('delivery-charges/{id}', [DeliveryChargeController::class, 'update']);
Route::middleware('auth:api')->delete('delivery-charges/{id}', [DeliveryChargeController::class, 'destroy']);

Route::get('/otp/generate', [OtpController::class, 'generateOtp']);
Route::post('/otp/verify', [OtpController::class, 'verifyOtp']);

Route::get('/otp/test', function () {
    $response = Http::post("http://localhost:8080/api/otp/generate?phoneNumber=0022247100763");
    return $response->json();
});

Route::middleware('auth:sanctum')->group(function () {


 
    Route::get('/users/locations', [UserController::class, 'getUserLocation']);
    Route::post('/users/locations', [UserController::class, 'storeUserLocation']);
    Route::put('/users/locations/{locationId}', [UserController::class, 'updateUserLocation']);
    Route::delete('/users/locations/{locationId}', [UserController::class, 'deleteUserLocation']);
    Route::post('/stores/toggle-status', [StoreController::class, 'toggleStoreStatus']);
    
    // Store discussion routes
    Route::post('store-discussions', [OfferController::class, 'createDiscussion']);
    Route::get('store-discussions', [OfferController::class, 'getUserDiscussions']);
    Route::get('store-discussions/{itemID}', [OfferController::class, 'getMessages']);
    Route::post('store-discussions/{itemID}/messages', [OfferController::class, 'sendMessage']);
});

Route::post('/password/request-reset', [AuthController::class, 'requestPasswordReset']);
Route::post('/password/reset', [AuthController::class, 'resetPassword']);


Route::middleware('auth:api')->get('auth/user', [AuthController::class, 'getUser']);

// Ad routes
Route::get('/ads/active', [AdController::class, 'getActiveAds']);

// Admin routes for ads management
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::get('/admin/ads', [AdController::class, 'index']);
    Route::post('/admin/ads', [AdController::class, 'store']);
    Route::put('/admin/ads/{id}', [AdController::class, 'update']);
    Route::delete('/admin/ads/{id}', [AdController::class, 'destroy']);
});




Route::get('/test-sftp', function () {
  

        try {
        $result = Storage::disk('sftp')->put('test-api.txt', 'API upload test ' . now());
        info('SFTP put result: ' . var_export($result, true));
    } catch (\Exception $e) {
        info('SFTP error: ' . $e->getMessage());
    }

});
