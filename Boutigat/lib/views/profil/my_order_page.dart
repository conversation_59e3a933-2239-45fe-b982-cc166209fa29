import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';
//import 'package:boutigak/data/services/snapchat_share.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:get/get.dart';
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/services.dart';
import 'package:boutigak/views/widgets/payment_widget.dart';
import 'package:boutigak/controllers/badge_controller.dart';
class MyOrdersPage extends StatefulWidget {
  final VoidCallback? onWillPop;

  const MyOrdersPage({Key? key, this.onWillPop}) : super(key: key);

  @override
  _MyOrdersPageState createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage> {
  final OrderController orderController = Get.put(OrderController());
  final BadgeController badgeController = Get.find<BadgeController>();




void _navigateToHome() async {
  Get.offAll(
    () => ZoomDrawerWrapper(shouldOpenDrawer: true), 
    transition: Transition.noTransition,
  );
}

  @override
  void initState() {
    super.initState();
    orderController.fetchedMyOrders();
    badgeController.resetBadge('user-order');
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        if (widget.onWillPop != null) {
          widget.onWillPop!();
        }
        return Future.value(true);
      },
      child: Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (widget.onWillPop != null) {
              widget.onWillPop!();
            } else {
                

                _navigateToHome();

            }
          },
        ),
        title: Text(
          'my_orders'.tr,
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.primary),
      ),
      body: Obx(() {
        if (orderController.myOrders.isEmpty) {
          return const Center(child: Text('No orders found.'));
        }

        return ListView.builder(
          itemCount: orderController.myOrders.length,
          itemBuilder: (context, index) {
            final order = orderController.myOrders[index];

            final double delivery =
                double.tryParse(order.deliveryCharge ?? '0') ?? 0.0;
            final double total =
                (order.totalOrders ?? 0).toDouble() + delivery;

            return GestureDetector(
  onTap: () {
    Get.to(() => OrderDetailsPage(order: order),
        transition: Transition.rightToLeft);
  },
  child: Container(
    margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
    padding: const EdgeInsets.all(8.0),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.0),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.3),
          spreadRadius: 1,
          blurRadius: 4,
          offset: const Offset(0.4, 0.4),
        ),
      ],
    ),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center, // centre les colonnes
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: order.storeImage != null
              ? CachedImageWidget(
                  imageUrl:
                      '${hostURLs[PossiblesHosts.STORAGE]}${order.storeImage}',
                  width: 70,
                  height: 70,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8.0),
                )
              : Image.asset(
                  'assets/default_store_image.png',
                  width: 70,
                  height: 70,
                  fit: BoxFit.cover,
                ),
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center, // centré verticalement
            children: [
              Text(
                order.storeName ?? 'Unknown Store',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _statusColor(order.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  order.status ?? 'N/A',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: _statusColor(order.status),
                  ),
                ),
              ),
            ],
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center, // centré verticalement
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '#${order.orderId ?? 'N/A'}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${total.toStringAsFixed(2)} UM',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ],
    ),
  ),
);


          },
        );
      }),
    ),
    );
  }

 Color _statusColor(String? status) {
  switch (status) {
    case 'PENDING':
      return Colors.grey; // ou Colors.amber selon ton design
    case 'ACCEPTED':
      return Colors.blue;
    case 'CANCELLED':
      return Colors.red;
    case 'WAITING_PAYMENT':
      return Colors.deepOrange; // ou une autre couleur distinctive
    case 'DELIVERED':
      return Colors.green;
    case 'COMPLETED':
      return Colors.green;
    default:
      return Colors.black26;
  }
}
}
class OrderDetailsPage extends StatelessWidget {
  final Order order;

  const OrderDetailsPage({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final OrderController orderController = Get.find<OrderController>();
    final double delivery = double.tryParse(order.deliveryCharge ?? '0') ?? 0.0;
    final double productsTotal = (order.totalOrders ?? 0).toDouble();
    final double total = productsTotal + delivery;

    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${order.orderId}'),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 200), // pour ne pas cacher le bas
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ---- En-tête Boutique ----
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: order.storeImage != null
                              ? Image.network(
                                  '${hostURLs[PossiblesHosts.STORAGE]}${order.storeImage}',
                                  width: 60,
                                  height: 60,
                                  fit: BoxFit.cover,
                                )
                              : Image.asset(
                                  'assets/default_store_image.png',
                                  width: 60,
                                  height: 60,
                                  fit: BoxFit.cover,
                                ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            order.storeName ?? 'Unknown Store',
                            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                          ),
                        ),
                        Chip(
                          label: Text(order.status ?? 'N/A',
                              style: const TextStyle(color: Colors.white)),
                          backgroundColor: _statusColor(order.status),
                        ),
                      ],
                    ),
                  ),
                 
                  // ---- Texte après les produits ----
Padding(
  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    
      const SizedBox(height: 6),
      Text(
        '${order.items.length} product${order.items.length > 1 ? 's' : ''} from ${order.storeName ?? "store"}',
        style: const TextStyle(fontSize: 16, color: AppColors.onSurface),
      ),
    ],
  ),
),

                  // ---- Liste des items ----
                  ...order.items.map((orderItem) {
                    final itemTag = orderItem.itemId.toString();
                    final itemController = Get.put(ItemController(), tag: itemTag);
                    itemController.fetchItemById(orderItem.itemId);

                    return Obx(() {
                      final item = itemController.selectedItem.value;
                      if (item == null) {
                        return const ListTile(
                          title: Text('Loading item…'),
                          subtitle: LinearProgressIndicator(),
                        );
                      }
                      final totalLine = item.price * orderItem.quantity;

                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: item.images != null
                              ? Image.network(
                                  '${hostURLs[PossiblesHosts.STORAGE]}${item.images.first}',
                                  width: 48,
                                  height: 60,
                                  fit: BoxFit.cover,
                                )
                              : null,
                          title: Text(item.title,
                              style: const TextStyle(fontWeight: FontWeight.w600)),
                          subtitle: Text(
                              '${orderItem.quantity} × ${item.price.toStringAsFixed(2)} UM'),
                          trailing: Text(
                            '${totalLine.toStringAsFixed(2)} UM',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      );
                    });
                  }),
                  const SizedBox(height: 120), // Pour laisser de l'espace avant le résumé
                ],
              ),
            ),
          ),

          // ---- Résumé collé en bas ----
          Align(
            alignment: Alignment.bottomCenter,
            child: ClipPath(
              clipper: ZigZagClipper(),
              child: Container(
                width: double.infinity,
                color: Colors.grey.shade200,
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 4),
                    Row(
                      
                      children: [

                        const Text(
                          'Summary',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    _rowLabelValue(
                        'Products Total', '${productsTotal.toStringAsFixed(2)} UM'),
                    const SizedBox(height: 8),
                    _rowLabelValue(
                        'Delivery Charge', '${delivery.toStringAsFixed(2)} UM'),
                    const SizedBox(height: 8),
                    _rowLabelValue('Total', '${total.toStringAsFixed(2)} UM',
                        isBold: true, color: Colors.green),
                    const SizedBox(height: 16),

                    if (order.status == 'ACCEPTED' &&
                        !order.isPaid &&
                        !order.isCashOnDelivery)
                      _buildPayButton(total, order),
                    if (!order.isPaid && order.status != 'CANCELLED')
                      _buildCancelButton(orderController, order),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _rowLabelValue(String label, String value,
      {bool isBold = false, Color? color}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(
          value,
          style: TextStyle(
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildPayButton(double total, Order order) {
    return Center(
      child: ElevatedButton(
        onPressed: () {
          Get.to(
            () => PaymentWidget(
              amount: total,
              orderId: order.orderId,
              isOrderPayment: true,
              isPaid: false,
              storeId: order.storeId,
              initialAmount: total,
            ),
          );
        },
        child: const Text('Pay Now'),
      ),
    );
  }

  Widget _buildCancelButton(OrderController orderController, Order order) {
    return Center(
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
        onPressed: () {
          Get.defaultDialog(
            title: 'Cancel Order',
            middleText: 'Are you sure you want to cancel this order?',
            textCancel: 'No',
            textConfirm: 'Yes',
            confirmTextColor: Colors.white,
            onConfirm: () {
              orderController.changeOrderStatus(order.orderId!, 'CANCELLED');
              Get.back(result: true);
            },
          );
        },
        child: const Text('Cancel Order'),
      ),
    );
  }

  Color _statusColor(String? status) {
    switch (status) {
      case 'PENDING':
        return Colors.grey;
      case 'ACCEPTED':
        return Colors.blue;
      case 'CANCELLED':
        return Colors.red;
      case 'WAITING_PAYMENT':
        return Colors.deepOrange;
      case 'DELIVERED':
      case 'COMPLETED':
        return Colors.green;
      default:
        return Colors.black26;
    }
  }
}

class ZigZagClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const double zigzagHeight = 5;
    const int zigzagCount = 70;

    final path = Path();
    path.lineTo(0, 0);

    for (int i = 0; i < zigzagCount; i++) {
      final x = size.width / zigzagCount * i;
      path.lineTo(
        x + size.width / zigzagCount / 2,
        i.isOdd ? zigzagHeight : 0,
      );
    }

    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
