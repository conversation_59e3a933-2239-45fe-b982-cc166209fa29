import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'boutiques_store_widgets.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';







class StorePage extends StatelessWidget {
  StorePage({Key? key}) : super(key: key);

  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;

  final StoreController storeController = Get.put(StoreController());
  final AuthController authController = Get.find<AuthController>();

  @override
  Widget build(BuildContext context) {
    if (storeController.recommendedStores.isEmpty) {
      storeController.fetchRecomandedStores();
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: PreferredSize(
  preferredSize: const Size.fromHeight(kToolbarHeight + 16),
  child: SafeArea(
    bottom: false, // on protège surtout le haut
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 4),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        children: [
          // Titre
          Text(
            'stores'.tr,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: AppTextSizes.heading,
              fontWeight: AppFontWeights.bold,
            ),
          ),
          const SizedBox(width: 12),
          
          // Barre de recherche
          Expanded(
            child: TextField(
              controller: searchController,
              onChanged: (value) => searchQuery.value = value,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search, size: 18),
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          const SizedBox(width: 12),
          
          // Avatar
         GestureDetector(
              onTap: () {
                if (!authController.isAuthenticated.value) {
                  Get.to(() => LoginPage());
                } else {
                  ZoomDrawer.of(context)!.toggle();
                }
              },
              child: Container(
                margin: EdgeInsets.only(
                  right: MediaQuery.of(context).size.width * 0.04,
                  left: MediaQuery.of(context).size.width * 0.04,
                ),
                child: authController.isAuthenticated.value
                    ? CircleAvatar(
                        radius: 22,
                        backgroundColor: Colors.grey[300],
                        child: Text(
                          '${authController.user?.firstName[0] ?? ''}${authController.user?.lastName[0] ?? ''}'.toUpperCase(),
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          border: Border.all(
                            color: Theme.of(context).colorScheme.onSurface,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          FontAwesomeIcons.solidUser,
                          size: 15,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
              ),
            ),
        ],
      ),
    ),
  ),
),

      body: Obx(() {
        if (storeController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (storeController.recommendedStores.isEmpty) {
          return const Center(child: Text("Aucune boutique disponible."));
        }

        final filteredStores = storeController.recommendedStores.where((store) {
          return searchQuery.value.isEmpty ||
              store.name.toLowerCase().contains(searchQuery.value.toLowerCase());
        }).toList();

        if (filteredStores.isEmpty) {
          return const Center(child: Text("Aucun résultat trouvé."));
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8,),
          itemCount: filteredStores.length,
          itemBuilder: (context, index) {
            final boutique = filteredStores[index];
            return BoutiqueWidget(
              boutique: boutique,
              fontSize: 18,
              iconSize: 12,
            );
          },
        );
      }),
    );
  }
}
