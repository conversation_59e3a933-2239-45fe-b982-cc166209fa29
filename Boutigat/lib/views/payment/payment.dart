import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart'
    as models; // Add prefix here
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/my_items.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:boutigak/controllers/payment_controller.dart';
import 'package:http/http.dart' as http;
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class PaymentWidget extends StatefulWidget {
  double amount;
  double initialAmount;
  final int? itemId; // Optional: For item payments
  final int? orderId; // Optional: For order payments
  final bool isOrderPayment; // Flag to determine if it's an order payment
  final bool isPaid; // Add this field
  final int? storeId; // Add this parameter

  PaymentWidget({
    super.key,
    required this.amount,
    required this.initialAmount,
    this.itemId,
    this.orderId,
    this.isOrderPayment = false, // Default to false
    this.isPaid = false, // Add this parameter
    this.storeId, // Add this parameter
  });

  @override
  _PaymentWidgetState createState() => _PaymentWidgetState();
}

class _PaymentWidgetState extends State<PaymentWidget> {
  final ImagePicker _picker = ImagePicker();
  final PaymentController paymentController = Get.put(PaymentController());
  XFile? _selectedImage;
  dynamic _selectedProvider; // Change to dynamic to handle both types
  List<dynamic> _providers = [];
  bool _isLoading = true;
  final TextEditingController _promoCodeController = TextEditingController();
  bool _isApplyingPromoCode = false;
  double? _discountedAmount;
  String? _promoCodeMessage;
  bool _isPromoCodeValid = false;

  Future<void> _applyPromoCode() async {
    if (_promoCodeController.text.isEmpty) {
      Get.snackbar(
        'Error',
        'Please enter a promo code',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isApplyingPromoCode = true;
      _promoCodeMessage = null;
    });

    try {
      final result = await PaymentService.verifyPromoCode(
        promoCode: _promoCodeController.text,
        itemId: widget.itemId!,
        originalAmount: widget.amount,
      );

      setState(() {
        _isApplyingPromoCode = false;
        _isPromoCodeValid = result['success'];
        _promoCodeMessage = result['message'];

        if (result['success']) {
          _discountedAmount = result['final_amount'].toDouble();

          widget.amount = _discountedAmount!;

          if (_discountedAmount == 0) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Payment added successfully')),
            );


            if (widget.orderId != null) {
              Get.to(MyOrdersPage());
            } else {
              Get.to(MyItemsPage());
              
            }
          }
        } else {
          _discountedAmount = null;
        }
      });

      Get.snackbar(
        result['success'] ? 'Success' : 'Error',
        result['message'],
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result['success'] ? Colors.green : Colors.red,
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() {
        _isApplyingPromoCode = false;
        _isPromoCodeValid = false;
        _promoCodeMessage = 'Error applying promo code';
      });
    }
  }

  // Helper method to get provider name
  String _getProviderName(dynamic provider) {
    if (provider is models.StorePaymentProvider) {
      return provider.providerName ?? '';
    } else if (provider is models.PaymentProvider) {
      return provider.name;
    }
    return '';
  }

  // Helper method to get provider logo
  String? _getProviderLogo(dynamic provider) {
    if (provider is models.StorePaymentProvider) {
      log('provider logo ${provider.providerLogo}');

      return provider.providerLogo;
    } else if (provider is models.PaymentProvider) {
      log('provider logoUrl ${provider.logoUrl}');

      return provider.logoUrl;
    }

    return null;
  }

  // Helper method to get provider code
  String _getProviderCode(dynamic provider) {
    if (provider is models.StorePaymentProvider) {
      return provider.paymentCode ?? '';
    } else if (provider is models.PaymentProvider) {
      return provider.providerCode;
    }
    return '';
  }

  // Helper method to get provider ID
  int _getProviderId(dynamic provider) {
    if (provider is models.StorePaymentProvider) {
      return provider.providerId!;
    } else if (provider is models.PaymentProvider) {
      return provider.id;
    }
    return 0;
  }

  Future<void> _loadPaymentProviders() async {
    try {
      setState(() => _isLoading = true);
      if (widget.isOrderPayment && widget.storeId != null) {
        // For order payments, fetch store payment providers by ID
        await paymentController.fetchStoreProvidersById(widget.storeId!);
        _providers = paymentController.storePaymentProviders;
      } else {
        // For other payments, fetch regular payment providers
        await paymentController.fetchPaymentProviders();
        _providers = paymentController.paymentProviders;
      }
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load payment providers: $e')),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _loadPaymentProviders();
  }

  OrderController orderController = OrderController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Page checkout',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
      ),
      body: widget.isPaid
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 64),
                  SizedBox(height: 16),
                  Text(
                    'Cette commande a déjà été payée',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            )
          : SafeArea(
              child: SingleChildScrollView(
                // Ajouté ici
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 2,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Amount Section
                          Text(
                            'Montant à payer',
                            style: TextStyle(
                                fontSize: 16, color: Colors.grey[600]),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${widget.amount.toStringAsFixed(2)} MRU',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 10),

                          widget.itemId != null
                              ? Card(
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Code Promo',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                controller:
                                                    _promoCodeController,
                                                decoration: InputDecoration(
                                                  hintText:
                                                      'Entrer votre code promo',
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  contentPadding:
                                                      const EdgeInsets
                                                          .symmetric(
                                                    horizontal: 12,
                                                    vertical: 8,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            ElevatedButton(
                                              onPressed: _isApplyingPromoCode
                                                  ? null
                                                  : _applyPromoCode,
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    AppColors.primary,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 16,
                                                  vertical: 12,
                                                ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                              child: _isApplyingPromoCode
                                                  ? const SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child:
                                                          CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                    Color>(
                                                                Colors.white),
                                                      ),
                                                    )
                                                  : const Text(
                                                      'Appliquer',
                                                      style: TextStyle(
                                                          color: Colors.white),
                                                    ),
                                            ),
                                          ],
                                        ),
                                        if (_promoCodeMessage != null) ...[
                                          const SizedBox(height: 8),
                                          Text(
                                            _promoCodeMessage!,
                                            style: TextStyle(
                                              color: _isPromoCodeValid
                                                  ? Colors.green
                                                  : Colors.red,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                        if (_discountedAmount != null) ...[
                                          const SizedBox(height: 16),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              const Text(
                                                'Prix initial:',
                                                style: TextStyle(
                                                    color: Colors.grey),
                                              ),
                                              Text(
                                                '${widget.initialAmount.toStringAsFixed(2)} MRU',
                                                style: const TextStyle(
                                                  decoration: TextDecoration
                                                      .lineThrough,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              const Text(
                                                'Prix après réduction:',
                                                style: TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              Text(
                                                '${_discountedAmount!.toStringAsFixed(2)} MRU',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: AppColors.primary,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                )
                              : Container(),
                          const SizedBox(height: 24),

                          // Payment Options
                          Text(
                            'Options de paiement',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Screenshot Payment Option
                          Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Paiement par capture d\'écran',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // const Text(
                                      //   'Sélectionnez votre mode de paiement',
                                      //   style: TextStyle(
                                      //     fontSize: 16,
                                      //     fontWeight: FontWeight.w500,
                                      //   ),
                                      // ),
                                      const SizedBox(height: 16),
                                      if (_isLoading)
                                        const Center(
                                            child: CircularProgressIndicator())
                                      else
                                        Container(
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                                color: Colors.grey[300]!),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButton<dynamic>(
                                              isExpanded: true,
                                              value: _selectedProvider,
                                              hint: const Padding(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 16),
                                                child: Text(
                                                    'Choisir un mode de paiement'),
                                              ),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16),
                                              items: _providers.map((provider) {
                                                return DropdownMenuItem<
                                                    dynamic>(
                                                  value: provider,
                                                  child: Row(
                                                    children: [
                                                      Image.network(
                                                        _getProviderLogo(
                                                                provider) ??
                                                            '',
                                                        width: 24,
                                                        height: 24,
                                                        errorBuilder: (context,
                                                                error,
                                                                stackTrace) =>
                                                            Icon(Icons.payment,
                                                                size: 24),
                                                      ),
                                                      SizedBox(width: 8),
                                                      Text(
                                                        _getProviderName(
                                                            provider),
                                                        style: const TextStyle(
                                                            fontSize: 16),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              }).toList(),
                                              onChanged: (dynamic provider) {
                                                setState(() =>
                                                    _selectedProvider =
                                                        provider);
                                              },
                                            ),
                                          ),
                                        ),
                                      _selectedProvider != null
                                          ? Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(height: 16),
                                                if (_getProviderCode(
                                                        _selectedProvider)
                                                    .isNotEmpty)
                                                  Text(
                                                    'Code de paiement: ${_getProviderCode(_selectedProvider)}',
                                                    style: TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                if (_selectedProvider is models
                                                        .StorePaymentProvider &&
                                                    (_selectedProvider as models
                                                                .StorePaymentProvider)
                                                            .phoneNumber !=
                                                        null)
                                                  Text(
                                                    'Numéro de téléphone: ${(_selectedProvider as models.StorePaymentProvider).phoneNumber}',
                                                    style: TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'Veuillez effectuer le paiement et télécharger la capture d\'écran ci-dessous',
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            )
                                          : Container(),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  ElevatedButton(
                                    onPressed: _selectedProvider == null
                                        ? null // Disable button if no provider is selected
                                        : () async {
                                            final XFile? image =
                                                await _picker.pickImage(
                                              source: ImageSource.gallery,
                                            );
                                            if (image != null) {
                                              print('here....');

                                              setState(
                                                  () => _selectedImage = image);

                                              bool success = widget.orderId !=
                                                      null
                                                  ? await orderController
                                                      .orderPayment(
                                                          providerId:
                                                              _getProviderId(
                                                                  _selectedProvider),
                                                          order_id:
                                                              widget.orderId!,
                                                          amount: widget.amount,
                                                          paymentImage:
                                                              File(image.path))
                                                  : await ItemService
                                                      .addPayment(
                                                      itemId: widget.itemId!,
                                                      providerId: _getProviderId(
                                                          _selectedProvider),
                                                      amount: widget.amount,
                                                      paymentType: 'photo',
                                                      paymentImage:
                                                          File(image.path),
                                                    );

                                              if (success) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                          'Payment added successfully')),
                                                );


                                                if (widget.orderId != null) {
              Get.to(MyOrdersPage());
            } else {
              Get.to(MyItemsPage());
              
            }
                                              } else {
                                                // Handle failure
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                          'Failed to add payment')),
                                                );
                                              }
                                            }
                                          },
                                    style: ElevatedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      backgroundColor: _selectedProvider == null
                                          ? Colors.grey
                                          : Theme.of(context)
                                              .colorScheme
                                              .primary,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: const [
                                          Icon(Icons.upload_file,
                                              color: Colors.white),
                                          SizedBox(width: 8),
                                          Text(
                                              'Télécharger la capture d\'écran'),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          widget.itemId != null
                              ? Card(
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Paiement par eBankily',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const SizedBox(height: 12),
                                        ElevatedButton(
                                          onPressed: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    BankilyCheckoutPage(
                                                  amount: widget.amount,
                                                  itemId: widget.itemId!,
                                                ),
                                              ),
                                            );
                                          },
                                          style: ElevatedButton.styleFrom(
                                            foregroundColor: Colors.white,
                                            backgroundColor: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                          ),
                                          child: const Padding(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 12),
                                            child: Center(
                                              child:
                                                  Text('Payer avec eBankily'),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}

class BankilyCheckoutPage extends StatefulWidget {
  final double amount;
  final int itemId;

  const BankilyCheckoutPage({
    Key? key,
    required this.amount,
    required this.itemId,
  }) : super(key: key);

  @override
  State<BankilyCheckoutPage> createState() => _BankilyCheckoutPageState();
}

class _BankilyCheckoutPageState extends State<BankilyCheckoutPage> {
  final TextEditingController _phoneController = TextEditingController();

  bool _isLoading = false;

  String _codeBpay = "";

  Future<void> _processPayment() async {
    if (_phoneController.text.isEmpty || _codeBpay.length < 4) {
      Get.snackbar(
        'Error',
        'Please enter both phone number and code',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await PaymentService.processBPayPayment(
        phone: _phoneController.text,
        passcode: _codeBpay,
        amount: widget.amount,
        itemId: widget.itemId.toString(),
      );

      setState(() => _isLoading = false);

      if (result['success']) {
        final transactionData = result['data'];

        _showPaymentSuccessDialog(transactionData);
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar(
        'Error',
        'Failed to process payment',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showPaymentSuccessDialog(Map<String, dynamic> transactionData) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          title: const Text(
            'Paiement Réussi',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Transaction ID : ${transactionData['transaction_id']}'),
              const SizedBox(height: 8),
              Text('Operation ID : ${transactionData['operation_id']}'),
              const SizedBox(height: 8),
              Text('Status : ${transactionData['status']}'),
              const SizedBox(height: 8),
              Text('Payment ID : ${transactionData['payment_id']}'),
              const SizedBox(height: 8),
              Text('Montant : ${transactionData['amount']} MRU'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();

                Get.offAll(() => NavigationBarPage());
              },
              child: const Text('OK'),
            ),
            ElevatedButton.icon(
              onPressed: () => _generateAndSharePdf(transactionData),
              icon: const Icon(Icons.picture_as_pdf),
              label: const Text('Télécharger PDF'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _generateAndSharePdf(
      Map<String, dynamic> transactionData) async {
    try {
      // 1. Créer une instance de Document
      final pdf = pw.Document();

      // 2. Ajouter une page avec le contenu
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Center(
                  child: pw.Text(
                    'Reçu de Paiement',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Text('Transaction ID: ${transactionData['transaction_id']}'),
                pw.Text('Operation ID: ${transactionData['operation_id']}'),
                pw.Text('Status: ${transactionData['status']}'),
                pw.Text('Payment ID: ${transactionData['payment_id']}'),
                pw.Text('Montant: ${transactionData['amount']} MRU'),
                pw.SizedBox(height: 10),
                pw.Text(
                  'Date de paiement: ${DateTime.now()}',
                  style: const pw.TextStyle(fontSize: 12),
                ),
                // Ajoutez d’autres champs si nécessaire...
              ],
            );
          },
        ),
      );

      // 3. Générer les bytes du PDF
      final pdfBytes = await pdf.save();

      // 4. Utiliser la fonction sharePdf(...) de printing pour partager
      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: 'receipt_bpay_${transactionData['transaction_id']}.pdf',
      );
    } catch (e) {
      // Gérer les erreurs éventuelles
      debugPrint('Erreur lors de la génération du PDF : $e');
      Get.snackbar(
        'Erreur PDF',
        'Impossible de générer le PDF',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: const Text(
          'B-Pay',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.surface,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              // Service Card
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        'Montant à payer',
                        style: TextStyle(fontSize: 24, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.amount.toStringAsFixed(2)} MRU',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 24),

              Container(
                padding: const EdgeInsets.only(left: 16, right: 24),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset(
                      'assets/images/bankily.jpg',
                      width: 100,
                      height: 80,
                    ),
                    Column(
                      children: [
                        const Text(
                          'Code B-PAY',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.disabled,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '003271',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                            letterSpacing: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Numéro de téléphone
              TextField(
                controller: _phoneController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(8), // ✅ Limite à 8 chiffres
                  FilteringTextInputFormatter
                      .digitsOnly, // ✅ Empêche les lettres/symboles
                ],
                decoration: InputDecoration(
                  hintText: 'Téléphone',
                  suffixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              CodeBpayForm(
                onCodeEntered: (code) {
                  setState(() {
                    _codeBpay = code;
                  });
                },
              ),

              const SizedBox(height: 64),

              // Confirm Button
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : CustomButton(
                      text: 'Continuer',
                      onPressed: _processPayment,
                    )
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();

    super.dispose();
  }
}

class CodeBpayForm extends StatefulWidget {
  final Function(String) onCodeEntered; // Callback pour récupérer le code B-PAY

  const CodeBpayForm({Key? key, required this.onCodeEntered}) : super(key: key);

  @override
  _CodeBpayFormState createState() => _CodeBpayFormState();
}

class _CodeBpayFormState extends State<CodeBpayForm> {
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  final List<TextEditingController> _controllers =
      List.generate(4, (index) => TextEditingController());

  @override
  void initState() {
    super.initState();

    // Écoute des changements pour envoyer le code au parent
    for (int i = 0; i < 4; i++) {
      _controllers[i].addListener(() {
        String enteredCode = _controllers.map((c) => c.text).join();
        widget.onCodeEntered(enteredCode);
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  /// Gère le passage automatique d'un champ à l'autre
  void _onChanged(String value, int index) {
    if (value.length > 1) {
      final characters = value.split('');
      for (int i = 0; i < characters.length; i++) {
        final pos = index + i;
        if (pos < 4) {
          _controllers[pos].text = characters[i];
        }
      }
      final nextPos = index + value.length;
      if (nextPos < 4) {
        FocusScope.of(context).requestFocus(_focusNodes[nextPos]);
      } else {
        FocusScope.of(context).unfocus();
      }
    } else {
      if (value.isNotEmpty && index < 3) {
        FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
      } else if (value.isEmpty && index > 0) {
        FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
      }
    }
    widget.onCodeEntered(_controllers.map((c) => c.text).join());
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
          width: 340,
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(4, (index) {
              return SizedBox(
                width: 50,
                height: 60,
                child: TextFormField(
                  controller: _controllers[index],
                  focusNode: _focusNodes[index],
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  textInputAction:
                      index < 3 ? TextInputAction.next : TextInputAction.done,
                  autofillHints: const [AutofillHints.oneTimeCode],
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(1),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChanged: (value) => _onChanged(value, index),
                  decoration: InputDecoration(
                    counterText: "",
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        Icon(Icons.lock),
      ],
    );
  }
}
