import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';




class StoreService {

 
  
static Future<List<StoreType>?> fetchStoreTypes() async {
    try {
      var response = await WebService.get(AvailableServices.storeTypes);
      print('Response status code store type : ${response.statusCode}');
      // print('response stores type ++++++++++++ ${response.body}+++++++++++++');
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<StoreType> storeTypes = body.map((dynamic storeType) => StoreType.fromJson(storeType)).toList();
        return storeTypes;
      } else {
        // Get.snakbar("Error", "Failed to load store types");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching store types: ${e.toString()}");
      return null;
    }
  }
  /// Méthode pour créer un nouveau store
  /// 
  static Future<bool> postStore(Store store) async {
    try {
      Map<String, String> fields = {
        'name': store.name,
        'description': store.description,
        'type_id': store.typeId.toString(),
        'opening_time': store.openingTime,
        'closing_time': store.closingTime,
      };

      List<File> files = [];
      for (var imagePath in store.images) {
        print('Preparing image for upload: $imagePath');
        files.add(File(imagePath));
      }

      var response = await WebService.poststoreMultipart(
        AvailableServices.stores,
        fields: fields,
        files: files,
      );

      var responseString = await response.stream.bytesToString();
      var responseJson = jsonDecode(responseString);

      print('Response status code: ${response.statusCode}');
      print('Response body: $responseJson');

      if (response.statusCode == 201) {
        // Get.snakbar("Success", "Store successfully created");
        return true;
      } else {
        // Get.snakbar("Error", "Failed to create store. Status: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      print('Error creating store: $e');
      // Get.snakbar("Error", "An error occurred while creating the store: ${e.toString()}");
      return false;
    }
  }

  // GET request to fetch favorite categories
  static Future<List<Category>?> fetchMyFavoriteCategories() async {
    var response = await WebService.get(AvailableServices.myStoreFavoritesCategories);
  //  print('Response status code: ${response.statusCode}');
  // print('Response body: ${response.body}');
    if (response.statusCode == 200) {
      List<dynamic> body = jsonDecode(response.body);
      List<Category> favoriteCategories = body.map((dynamic item) => Category.fromJson(item)).toList();
      return favoriteCategories;
    } else {
      // Get.snakbar("Error", "Failed to load my favorite categories");
      return null;
    }
  }

  
static Future<bool> addFavoriteCategory(int categoryId) async {
  // Créez le corps de la requête
  Map<String, dynamic> requestBody = {
    "category_id": categoryId,
  };

  // Affichez le JSON envoyé
  // print('JSON envoyé: ${requestBody}');

  // Envoyez la requête POST
  final response = await WebService.post(
    AvailableServices.storeAddFavoritesCategory,
    body: requestBody,
  );

 
  // print('Response status code: ${response.statusCode}');
  // print('Response body: ${response.body}');

  // Vérifiez le statut de la réponse
  if (response.statusCode == 201) {
    // Get.snakbar("Success", "Category added to favorites");
    return true;
  } else {
    // Get.snakbar("Error", "Failed to add favorite category");
    return false;
  }
}

  // Méthode pour récupérer les stores existants
  static Future<List<Store>?> fetchPromotedStores() async {
    try {
      var response = await WebService.get(AvailableServices.promotedStores);
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);


        // print('response stores ${body}');
        List<Store> stores = body.map((dynamic store) => Store.fromJson(store)).toList();
        return stores;
      } else {
        // Get.snakbar("Error", "Failed to load stores");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while  stores: ${e.toString()}");
      return null;
    }
  }
static Future<List<Store>?> fetchRecomandedStores() async {
    try {
      var response = await WebService.get(AvailableServices.generalrecomandedStores);
      // Affichez le statut de la réponse et son corps
  // print('Response status code genral : ${response.statusCode}');
  // print('Response body in recomnered : ${response.body}');



      log('Response stores : ${response.body}');
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);


      //  print('stores ${body}');
        List<Store> stores = body.map((dynamic store) => Store.fromJson(store)).toList();
        return stores;
      } else {
     //   // Get.snakbar("Error", "Failed to load stores");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching stores: ${e.toString()}");
    //  print('Response body: ${e}');
      return null;
    }
  }
  static Future<Map<String, dynamic>?> postStoreItemWithProgress(
  Item item,
  Function(double) onUploadProgress,
) async {
  try {
    Map<String, String> fields = {
      'id': item.id?.toString() ?? '',
      'title': item.title,
      'description': item.description,
      'price': item.price.toString(),
      'condition': item.condition,
      'quantity': item.quantity.toString(),
      'brand_id': item.brandId.toString(),
      'category_id': item.categoryId.toString(),
      'category_detail_id': item.categoryDetailId?.toString() ?? '',
    };

    // Ajouter les détails de catégorie
    if (item.categoryItemDetails.isNotEmpty) {
      for (var i = 0; i < item.categoryItemDetails.length; i++) {
        fields['category_item_details[$i][id]'] = item.categoryItemDetails[i].id.toString();
        fields['category_item_details[$i][value]'] = item.categoryItemDetails[i].value;
        fields['category_item_details[$i][label_en]'] = item.categoryItemDetails[i].labelEn;
        fields['category_item_details[$i][label_ar]'] = item.categoryItemDetails[i].labelAr;
        fields['category_item_details[$i][label_fr]'] = item.categoryItemDetails[i].labelFr;
      }
    }

    // Convertir les chemins d’images en fichiers
    List<File> files = item.images.map((imagePath) => File(imagePath)).toList();

    // Envoi multipart avec suivi de progression
    var response = await WebService.postMultipart(
      AvailableServices.postStoreItem,
      fields: fields,
      files: files,
      onSendProgress: (int sent, int total) {
        double progress = sent / total;
        onUploadProgress(progress);
      },
    );

    var responseString = await response.stream.bytesToString();
    var responseJson = jsonDecode(responseString);

    print('Response status code: ${response.statusCode}');
    print('Response body: $responseJson');

    if (response.statusCode == 201) {
      return {
        'categoryPrice': double.tryParse(responseJson['categoryPrice'].toString()) ?? 0.0,
        'item': responseJson['item'],
      };
    } else {
      return null;
    }
  } catch (e) {
    print('Error posting store item: $e');
    return null;
  }
}


 static Future<List<Item>?> fetchMyStoreItems() async {
    try {
      var response = await WebService.get(AvailableServices.getMyStoreItem);
  // print('Response status code myitemStore : ${response.statusCode}');
 // print('Response body: ${response.body}');
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Item> items = body.map((dynamic item) => Item.fromJson(item)).toList();
        return items;
      } else {
        // Get.snakbar("Error", "Failed to load items");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching items ${e.toString()}");
      return null;
    }
  }



  static Future<List<Item>?> fetchStoreItems(String storeId) async {
    try {
      String url = AvailableServices.storeItems.replaceAll("{storeId}",storeId.toString());
      var response = await WebService.get(url);
      
     print('================================Response status store items: ${response.statusCode}');
     print('Response body: ${response.body}');
      
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Item> items = body.map((dynamic item) => Item.fromJson(item)).toList();
        return items;
      } else {
        // Get.snakbar("Error", "Failed to load items");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching items: ${e.toString()}");
      return null;
    }
  }



  static Future<bool> deleteStoreFavoriteCategory(int categoryId) async {
    try {
      // Construire l'URL de suppression avec l'ID de l'élément
      String url = AvailableServices.deleteStoreFavoriteCategory.replaceAll("{categoryId}", categoryId.toString());

      // Appeler le service Web pour effectuer la requête DELETE
      var response = await WebService.delete(url);

      if (response.statusCode == 200 || response.statusCode == 204) {
        // Get.snakbar("Success", "Category successfully deleted");
        return true;
      } else {
        // Get.snakbar("Error", "Failed to delete category");
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while deleting the category: ${e.toString()}");
      return false;
    }
  }
static Future<bool> deleteStoreItem(int itemId) async {
    try {
      // Construire l'URL de suppression avec l'ID de l'élément
      String url = AvailableServices.deleteStoreItem.replaceAll("{itemId}", itemId.toString());


     log('url ${url}');
      // Appeler le service Web pour effectuer la requête DELETE
      var response = await WebService.delete(url);
log('Response from deletestoreitems: ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 204) {
        Get.snackbar("Success", "Item successfully deleted");
        return true;
      } else {
        Get.snackbar("Error", "Failed to delete item");
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while deleting the item: ${e.toString()}");
      return false;
    }
  }


 static Future<Map<String, dynamic>?> storeMyStoreLocation(Map<String, dynamic> locationData) async {
  try {
    var response = await WebService.post(AvailableServices.myStoreLocation, body: locationData);

    print('Response from storeMyStoreLocation: ${response.body}');

    if (response.statusCode == 200 || response.statusCode == 201) {
      return jsonDecode(response.body);
    } else {
      return null;
    }
  } catch (e) {
    print('Error storing store location: $e');
    return null;
  }
}


  static Future<Location?> fetchMyStoreLocation() async {
  try {
    var response = await WebService.get(AvailableServices.myStoreLocation);

    print('Response from fetchMyStoreLocations: ${response.body}');

    if (response.statusCode == 200) {
      Map<String, dynamic> data = jsonDecode(response.body);
      return Location.fromJson(data);
    } else {
      return null;
    }
  } catch (e) {
    print('Error fetching store location: $e');
    return null;
  }
}


static Future<List<Category>?> fetchFavoriteCategories(String storeId) async {
    try {
      // Build the URL by replacing {storeId} with the actual storeId
      String url = AvailableServices.storeFavoritesCategories.replaceAll("{storeId}", storeId.toString());
      
      // Make the API call
      var response = await WebService.get(url);

     print(' Response fetchFavoriteCategories code follow store: ${response.statusCode}');
     print('Response fetchFavoriteCategories: ${response.body}');

      // Check if response is successful
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Category> favoriteCategories = body.map((dynamic item) => Category.fromJson(item)).toList();
        return favoriteCategories;
      } else {
        // Get.snakbar("Error", "Failed to load my favorite categories");
        return null;
      }
    } catch (e) {
      // Handle any errors that occur during the API call
      // Get.snakbar("Error", "An error occurred while fetching favorite categories: ${e.toString()}");
      return null;
    }
  }



   static Future<bool> followUnfollowStore(int storeId, bool isFollowing) async {
  try {
    String action = isFollowing ? 'unfollow' : 'follow';
    String url = AvailableServices.followUnfollowStore.replaceAll("{storeId}", storeId.toString());

    var response = await WebService.put(url);
 //   print('//////////////////////////////Response status code store favorite categories: ${response.statusCode}');
 //     print('Response body: ${response.body}');
    if (response.statusCode == 200) {
      var responseBody = jsonDecode(response.body);
      if (isFollowing && responseBody['message'] == 'Store unfollowed successfully') {
        // Get.snakbar("Success", "Store successfully unfollowed");
        return true;
      } else if (!isFollowing && responseBody['message'] == 'Store followed successfully') {
        // Get.snakbar("Success", "Store successfully followed");
        return true;
      } else {
        // Get.snakbar("Error", "Failed to ${action} store");
        return false;
      }
    } else {
      // Get.snakbar("Error", "Failed to ${action} store");
      return false;
    }
  } catch (e) {
    // Get.snakbar("Error", "An error occurred while trying to ${e} the store");
    return false;
  }
}




static Future<List<Store>?> fetchFollowedStores() async {
    try {
      var response = await WebService.get(AvailableServices.followedStores);
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Store> stores = body.map((dynamic store) => Store.fromJson(store)).toList();
        return stores;
      } else {
        // Get.snakbar("Error", "Failed to load stores");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching followed stores: ${e.toString()}");
      return null;
    }
  }





   static Future<Store?> getMyStoreInformation() async {
    try {
      
      
      var response = await WebService.get(AvailableServices.myStore);
      log('Response status code get my store: ${response.statusCode}');
    
     log('response ${response.body}');
      if (response.statusCode == 200) {
        var body = jsonDecode(response.body);
        Store store = Store.fromJson(body); // Créez un objet Item à partir de la réponse JSON
        return store;
      } else {
        // Get.snakbar("Error", "Failed to fetch mystore details");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching mystore insformation ${e.toString()}");
      return null;
    }
  }


   static Future<Store?> getStoreById(int storeId) async {
    try {
    

      var response = await WebService.get('${AvailableServices.stores}/$storeId');


    // print('Response   getStoreById status code get my store: ${response.statusCode}');
    
    //  print('getStoreById response ${response.body}');


      if (response.statusCode == 200) {
        var body = jsonDecode(response.body);

        return Store.fromJson(body);
      }
      return null;
    } catch (e) {
      print('Error fetching store by ID: $e');
      return null;
    }
  }

static Future<bool> setPromotionForItem(int itemId, double promotionPercentage) async {
  // Créez le corps de la requête
  Map<String, dynamic> requestBody = {
    "promotion_percentage": promotionPercentage,
  };

  // Construire l'URL avec l'ID de l'item
  String url = AvailableServices.setPromotionForItemInMyStore.replaceFirst("{itemId}", itemId.toString());

  // Envoyez la requête POST
  final response = await WebService.post(
    url,
    body: requestBody,
  );

  // Debug: Affichage des logs
  print('Request URL: $url');
  print('Request Body: $requestBody');
  print('Response status code: ${response.statusCode}');
  print('Response body: ${response.body}');

  // Vérifiez le statut de la réponse
  if (response.statusCode == 200) {
    // Get.snakbar("Success", "Promotion set successfully");
    return true;
  } else {
    // Get.snakbar("Error", "Store not found or Item not found");
    return false;
  }
}



  static Future<bool> removePromotionForItem(int itemId) async {
    try {
      String url = AvailableServices.removePromotionForItemInMyStore.replaceFirst("{itemId}", itemId.toString());

      final response = await WebService.delete(url);

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", 'Error in removePromotionForItem: $e');
   
      return false;
    }
  
}

/// Method to update an existing store
static Future<bool> updateStore(Store store) async {
  try {
    Map<String, String> fields = {
      'name': store.name,
      'description': store.description ?? '',
      'type_id': store.typeId.toString(),
      'opening_time': store.openingTime ?? '09:00',
      'closing_time': store.closingTime ?? '21:00',
    };

    List<File> files = [];
    if (store.images != null && store.images!.isNotEmpty) {
      for (var imagePath in store.images!) {
        if (imagePath.startsWith('http')) {
          // Skip existing images that are already on the server
          continue;
        }
        print('Preparing image for upload: $imagePath');
        files.add(File(imagePath));
      }
    }


    log('feilds ${fields}');

    var response = await WebService.postMultipart(
      AvailableServices.updateStore,
      fields: fields,
      files: files,
    );

    var responseString = await response.stream.bytesToString();
    var responseJson = jsonDecode(responseString);

    log('Response status code: ${response.statusCode}');
    log('Response body: $responseJson');

    if (response.statusCode == 200) {
      return true;
    } else {
      print('Error updating store: ${responseJson}');
      return false;
    }
  } catch (e) {
    print('Exception updating store: $e');
    return false;
  }
}














}

