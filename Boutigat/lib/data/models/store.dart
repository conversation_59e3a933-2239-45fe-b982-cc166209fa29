import 'package:intl/intl.dart';
class Store {
  int? id;
  final String name;
  final String description;
  final int typeId;
  final String openingTime;
  final String closingTime;
  final List<String> images; 
  final String? typeName;
  int followersCount; 
  int itemsCount;
  bool isFollowed; // Ajout de l'attribut isFollowed
  bool? isOpen;
  final double? latitude;
  final double? longitude;

  Store({
    this.id,
    required this.name,
    required this.description,
    required this.typeId,
     this.typeName,
    required this.openingTime,
    required this.closingTime,
    required this.images,
    this.followersCount = 0, 
    this.itemsCount = 0,
    this.isFollowed = false, // Valeur par défaut false
    this.isOpen,
    this.latitude,
    this.longitude,
  });

  // Factory pour créer un Store à partir d'un JSON
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      typeId: json['type_id'] is String ? int.parse(json['type_id']) : json['type_id'],
      typeName: json['type_name'],
      openingTime: json['opening_time'],
      closingTime: json['closing_time'],
      images: (json['images'] as List<dynamic>?) 
          ?.map((image) => image['media']['url'] as String) 
          .toList() ?? [], 
      followersCount: json['followers_count'] ?? 0, 
      itemsCount: json['items_count'] ?? 0, 
      isFollowed: json['is_followed'] ?? false, 
      isOpen: json['is_open'] ?? false,
      latitude: json['latitude'] != null ? (json['latitude'] as num).toDouble() : null,
      longitude: json['longitude'] != null ? (json['longitude'] as num).toDouble() : null,
      // type_name  
    );
  }


  // Méthode pour convertir un Store en JSON
Map<String, dynamic> toJson() {
  return {
    'id': id?.toString(),
    'name': name,
    'description': description,
    'type_id': typeId.toString(),
    'opening_time': openingTime,
    'closing_time': closingTime,
    'images': images.map((image) => {'image': image}).toList(),
    'is_open': isOpen,
    'latitude': latitude,
    'longitude': longitude,
  };
}

bool isOpenNow() {
  try {
    // Si l'attribut explicite isOpen est défini, on l'utilise
    if (isOpen != null) {
      return isOpen!;
    }

    // Sinon, on utilise les horaires d'ouverture
    final now = DateTime.now();
    final format = DateFormat.Hm(); // HH:mm

    final opening = format.parse(openingTime);
    final closing = format.parse(closingTime);

    final todayOpening = DateTime(now.year, now.month, now.day, opening.hour, opening.minute);
    DateTime todayClosing = DateTime(now.year, now.month, now.day, closing.hour, closing.minute);

    // Si le magasin ferme après minuit
    if (closing.hour < opening.hour) {
      todayClosing = todayClosing.add(const Duration(days: 1));
    }

    return now.isAfter(todayOpening) && now.isBefore(todayClosing);
  } catch (e) {
    return false;
  }
}



}

class StoreType {
  final int id;
  final String name;

  StoreType({
    required this.id,
    required this.name,
  });

  // Factory pour créer un StoreType à partir d'un JSON
  factory StoreType.fromJson(Map<String, dynamic> json) {
    return StoreType(
      id: json['id'],
      name: json['name'],
    );
  }
}
