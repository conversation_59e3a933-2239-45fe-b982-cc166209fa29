import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/models/categories.dart';
class Item {
  final int? id;
  final int? storeId;
  final String title;
  final String? titleAr;
  final String description;
  final String? descriptionAr;
  final double price;
  final String condition;
  final int? quantity;
  final int? brandId;
  final int categoryId;
  int? categoryDetailId;
  final List<CategoryItemDetail> categoryItemDetails;
  List<String> images;
  final Brands? brand;
  final Category? category;
  final bool? isPromoted;
  bool isLiked; 
  final String? userName; 
  final String? brandName; 
  final String? categoryName; 
  final DateTime? createdAt; 
  final String? matterportLink;
  final bool hasPromotion; 
  final double? promotionPercentage; 


  Item({
    required this.title,
    this.titleAr,
    required this.description,
    this.descriptionAr,
    required this.price,
    required this.condition,
    required this.quantity,
    required this.brandId,
    required this.categoryId,
    required this.categoryItemDetails,
    required this.images,
    this.isPromoted,
    this.brand,
    this.category,
    this.id,
    this.storeId,
    this.matterportLink,
    this.isLiked = false, 
    this.userName,
    this.brandName,
    this.categoryName,
    this.createdAt,
    this.hasPromotion = false, 
    this.promotionPercentage,
    this.categoryDetailId,
  });

  // Parsing JSON et ajout des nouveaux champs
  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['id'],
      storeId: json['store_id'],
      title: json['title'],
      titleAr: json['title_ar'],
      description: json['description'],
      descriptionAr: json['description_ar'],
      isPromoted: json['is_promoted'],
      price: json['price'] is String ? double.parse(json['price']) : json['price'].toDouble(),
      condition: json['condition'],
      quantity: json['quantity'] ?? 1,
      brandId: json['brand_id'] is String ? int.parse(json['brand_id']) : json['brand_id'],
      categoryId: json['category_id'] is String ? int.parse(json['category_id']) : json['category_id'],
      categoryItemDetails: (json['category_item_details'] as List<dynamic>?)
          ?.map((detail) => CategoryItemDetail.fromJson(detail))
          .toList() ?? [],
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => image['url'] as String)
          .toList() ?? [],
      category: json.containsKey('category') ? Category.fromJson(json['category']) : null,
      brand: json.containsKey('brand') ? Brands.fromJson(json['brand']) : null,
      isLiked: json['is_liked'] ?? false,

      // Assignation des nouveaux champs
      userName: json['user'] != null ? "${json['user']['firstname']} ${json['user']['lastname']}" : null,
      brandName: json['brand'] != null ? json['brand']['name'] : null,
      categoryName: json['category'] != null ? json['category']['title_en'] : null,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,

      // Champs promotionnels
      matterportLink: json["matterport_link"] ?? "",
      hasPromotion: json['has_promotion'] ?? false,
      promotionPercentage: json['promotion_percentage'] != null
          ? double.tryParse(json['promotion_percentage'].toString()) // Assure la conversion
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id?.toString(),
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'price': price.toString(),
      'condition': condition,
      'quantity': quantity?.toString(),
      'brand_id': brandId?.toString(),
      'category_id': categoryId?.toString(),
      'category_detail_id': categoryDetailId?.toString(),
      'category_item_details': categoryItemDetails.map((detail) => detail.toJson()).toList(),
      'images': images.map((image) => {'value': image}).toList(),
    };
  }
}


class CategoryItemDetail {
  int id;
  String labelEn;
  String labelAr;
  String labelFr;
  String value;

  CategoryItemDetail({
    required this.id,
    this.labelEn = '',
    this.labelAr = '',
    this.labelFr = '',
    required this.value,
  });

  factory CategoryItemDetail.fromJson(Map<String, dynamic> json) {
    return CategoryItemDetail(
      id: json['id'] ?? 0,
      labelEn: json['label_en'] ?? '',
      labelAr: json['label_ar'] ?? '',
      labelFr: json['label_fr'] ?? '',
      value: json['value'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label_en': labelEn,
      'label_ar': labelAr,
      'label_fr': labelFr,
      'value': value,
    };
  }
}

enum ItemUploadState {
  idle,       // Quand aucun téléchargement n'est en cours
  loading,    // Quand un article est en cours d'upload
  success,    // Quand un article est uploadé avec succès
  error       // Quand une erreur survient pendant l'upload
}
