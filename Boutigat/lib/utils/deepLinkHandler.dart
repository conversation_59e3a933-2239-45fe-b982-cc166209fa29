import 'dart:async';
import 'dart:developer';
import 'dart:ui';
import 'package:app_links/app_links.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/item_bottom_sheet_content.dart';
import 'package:boutigak/views/widgets/text_row_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;

class DeepLinkHandler {
  static final AppLinks _appLinks = AppLinks();
  static StreamSubscription<Uri>? _linkSubscription;
  static final ItemController itemController = Get.find<ItemController>();
  static final StoreController storeController = Get.put(StoreController());

  static Future<void> initDeepLinks() async {
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (Uri uri) {
        _handleDeepLink(uri);
      },
      onError: (err) {
        debugPrint('Deep link error: $err');
      },
    );
  }

  static Future<void> _handleDeepLink(Uri uri) async {
    debugPrint('Received deep link: ${uri.toString()}');

    if (uri.host == 'app.boutigak.com') {
      final segments = uri.pathSegments;

      log('segments: $segments');
      if (segments.length == 2) {
        // Handle /stores/{id} or /items/{id}

        log('segments 0: ${segments[0]}');

        switch (segments[0]) {
          case 'stores':
            await _navigateToStore(segments[1]);
            break;
          case 'items':
            await _navigateToItem(segments[1]);
            break;
        }
      } else if (segments.length == 4 &&
          segments[0] == 'stores' &&
          segments[2] == 'items') {
        // Handle /stores/{store_id}/items/{item_id}
        await _navigateToStoreAndItem(segments[1], segments[3]);
      }
    }
  }

  static Future<void> _navigateToStoreAndItem(
      String storeId, String itemId) async {
    try {
      final int parsedStoreId = int.parse(storeId);
      final int parsedItemId = int.parse(itemId);

      // Fetch store details
      Store? store = await StoreService.getStoreById(parsedStoreId);

      if (store == null) {
        // Get.snakbar('Error', 'Store not found');
        return;
      }

      print('parsedItemId: $parsedItemId');

      // Navigate to store with the selected item ID
      Get.to(() => StoreDetailsPage(
            store: store,
            selectedItemId: parsedItemId,
          ));
    } catch (e) {
      debugPrint('Error navigating to store and item: $e');
    }
  }

  static Future<void> _navigateToStore(String storeId) async {
    try {
      final int id = int.parse(storeId);

      // Get.dialog(
      //   const Center(child: CircularProgressIndicator()),
      //   barrierDismissible: false,
      // );

      Store? store = await StoreService.getStoreById(id);

      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (store != null) {
        Get.to(() => StoreDetailsPage(store: store));
      } else {
        // Get.snakbar(
        //   'Error',
        //   'Could not find the store',
        //   snackPosition: SnackPosition.BOTTOM,
        // );
      }
    } catch (e) {
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      debugPrint('Error navigating to store: $e');
      // Get.snakbar(
      //   'Error',
      //   'Could not load the store',
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    }
  }

  static Future<void> _navigateToItem(String itemId) async {
    try {
      final int id = int.parse(itemId);

      await itemController.fetchItemById(id);

      if (itemController.selectedItem.value != null) {
        Get.to(() => ItemDetailsPage(item: itemController.selectedItem.value!));
      }
    } catch (e) {
      debugPrint('Error navigating to item: $e');
      // Get.snakbar(
      //   'Error',
      //   'Could not load the item',
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    }
  }

  static void dispose() {
    _linkSubscription?.cancel();
  }
}

// Create a dedicated page for deep link navigation
class ItemDetailsPage extends StatelessWidget {
  final Item item;

  const ItemDetailsPage({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Reuse your existing ItemWidget bottom sheet content
            ItemBottomSheetContent(item: item),
          ],
        ),
      ),
    );
  }
}

class ItemBottomSheetWidget extends StatefulWidget {
  final Item item;
  final Store store;
  final bool isFromDeepLink;

  const ItemBottomSheetWidget({
    Key? key,
    required this.item,
    required this.store,
    this.isFromDeepLink = false,
  }) : super(key: key);

  static Future<void> show({
    required BuildContext context,
    required Item item,
    required Store store,
    bool isFromDeepLink = false,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => ItemBottomSheetWidget(
        item: item,
        store: store,
        isFromDeepLink: isFromDeepLink,
      ),
    );
  }

  @override
  State<ItemBottomSheetWidget> createState() => _ItemBottomSheetWidgetState();
}

class _ItemBottomSheetWidgetState extends State<ItemBottomSheetWidget> {
  final PageController pageController = PageController();
  final OrderController orderController = Get.put(OrderController());
  final ConversationController conversationController =
      Get.put(ConversationController());
  final AuthController authController = Get.put(AuthController());

  int currentPage = 0;
  int quantity = 1;
  late bool isLiked;

  @override
  void initState() {
    super.initState();
    isLiked = widget.item.isLiked;
  }

  Future<void> _toggleFavorite() async {
    final success = await ItemService.likeUnlikeItem(widget.item.id!, !isLiked);
    if (success) {
      setState(() => isLiked = !isLiked);
    }
  }

  void _addToOrder() {
    var existingOrderItem = orderController.items.firstWhereOrNull(
      (orderItem) => orderItem.itemId.value == widget.item.id,
    );

    if (existingOrderItem != null) {
      existingOrderItem.incrementQuantity();
    } else {
      var newItem = OrderItemController(
        initialItemId: widget.item.id!,
        initialQuantity: quantity,
      );
      orderController.addItem(newItem);
    }

    orderController.calculateTotal();
    Navigator.pop(context);
    orderController.update();
  }

  Future<void> _handleAskAbout() async {
    if (authController.isAuthenticated.value) {
      final success = await conversationController.createStoreDiscussion(
        widget.item.id!,
        widget.store.id!,
      );
      if (success != null) {
        Get.to(() => ConversationPage(
              itemId: widget.item.id!,
              item: widget.item.toJson(),
              discussionId: int.parse(success['discussion']['id'].toString()),
              interlocutor: widget.store.name,
              isStoreDiscussion: true,
            ));
      }
    } else {
      Get.to(() => LoginPage());
    }
  }

  Future<void> _shareItem() async {
    String deepLink =
        'https://app.boutigak.com/stores/${widget.store.id}/items/${widget.item.id}';
    await Share.share('Découvrez ce produit : $deepLink');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          // Top image section
          Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    ImageSlider(
                      pageController: pageController,
                      photos: widget.item.images
                          .map((image) =>
                              '${hostURLs[PossiblesHosts.STORAGE]}$image')
                          .toList(),
                      currentPage: currentPage,
                      onPageChanged: (page) =>
                          setState(() => currentPage = page),
                      borderRadius: 0,
                    ),
                    _buildShareButton(),
                    _buildCloseButton(),
                  ],
                ),
              ),
              Expanded(child: Container()),
            ],
          ),
          // Info section
          Positioned(
            top: MediaQuery.of(context).size.height * 0.49,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(15)),
              ),
              child: StoreItemInfoSectionWidget(
                boutiquePhotoUrl:
                    '${hostURLs[PossiblesHosts.STORAGE]}${widget.store.images.first}',
                item: widget.item,
                isFavorited: isLiked,
                toggleFavorite: _toggleFavorite,
              ),
            ),
          ),

          // Bottom actions
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 200,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(15)),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.only(bottom: 30.0, left: 25, right: 25),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildQuantitySelector(),
                    const SizedBox(height: 20),
                    _buildActionsAndPrice(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton() {
    return Positioned(
      top: 60,
      right: 10,
      child: ClipOval(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
          child: Container(
            color: AppColors.onBackground.withOpacity(0.2),
            child: IconButton(
              iconSize: 20,
              icon: const Icon(FontAwesomeIcons.upRightFromSquare,
                  color: AppColors.background),
              onPressed: _shareItem,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: 60,
      left: 10,
      child: ClipOval(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
          child: Container(
            color: AppColors.onBackground.withOpacity(0.3),
            child: IconButton(
              iconSize: 20,
              icon: const Icon(FontAwesomeIcons.chevronDown,
                  color: AppColors.background),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Quantity',
          style: TextStyle(fontSize: 16, fontWeight: AppFontWeights.medium),
        ),
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(15),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildQuantityButton(
                icon: Icons.remove,
                onTap: () => setState(() {
                  if (quantity > 1) quantity--;
                }),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13),
                child: Text(
                  quantity.toString(),
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              _buildQuantityButton(
                icon: Icons.add,
                onTap: () => setState(() => quantity++),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityButton(
      {required IconData icon, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Icon(icon, size: 16),
      ),
    );
  }

  Widget _buildActionsAndPrice() {
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Price (mru)",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            Text(
              "${(widget.item.price * quantity).toStringAsFixed(0)}",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const Spacer(),
        Row(
          children: [
            _buildOutlinedAction("Ask about", _handleAskAbout),
            const SizedBox(width: 10),
            _buildFilledAction("Add to Cart", _addToOrder),
          ],
        ),
      ],
    );
  }

  Widget _buildOutlinedAction(String text, VoidCallback onPressed) {
    return Container(
      width: 95,
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primary),
        borderRadius: BorderRadius.circular(20),
      ),
      child: TextButton(
        onPressed: onPressed,
        child: Text(
          text,
          style: const TextStyle(
            color: AppColors.primary,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildFilledAction(String text, VoidCallback onPressed) {
    return Container(
      width: 95,
      height: 50,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextButton(
        onPressed: onPressed,
        child: Text(
          text,
          style: const TextStyle(
            color: AppColors.primary,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}

class StoreItemInfoSectionWidget extends StatefulWidget {
  final String boutiquePhotoUrl;
  final Item item;
  final bool isFavorited;
  final VoidCallback? toggleFavorite;

  const StoreItemInfoSectionWidget({
    Key? key,
    required this.boutiquePhotoUrl,
    required this.item,
    required this.isFavorited,
    this.toggleFavorite,
  }) : super(key: key);

  @override
  State<StoreItemInfoSectionWidget> createState() =>
      _StoreItemInfoSectionWidgetState();
}

class _StoreItemInfoSectionWidgetState
    extends State<StoreItemInfoSectionWidget> {
  bool isDescriptionSelected = true;
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    isFavorited = widget.isFavorited;
  }

  @override
  void didUpdateWidget(covariant StoreItemInfoSectionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.item != oldWidget.item) {
      isFavorited = widget.item.isLiked;
    }
  }

  Future<void> _toggleFavorite() async {
    bool success =
        await ItemService.likeUnlikeItem(widget.item.id!, isFavorited);
    if (success) {
      setState(() {
        isFavorited = !isFavorited;
      });
      widget.toggleFavorite?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Titre, image boutique, bouton favori
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image boutique
              Container(
                width: 50,
                height: 50,
                margin: const EdgeInsets.only(right: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  image: DecorationImage(
                    image: NetworkImage(widget.boutiquePhotoUrl),
                    fit: BoxFit.cover,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: Offset(0.5, 0.5),
                    ),
                  ],
                ),
              ),
              // Titre + vendeur
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.title,
                        style: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Text("by ",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            )),
                        Text(widget.item.userName ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).disabledColor,
                              fontWeight: FontWeight.bold,
                            )),
                      ],
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: _toggleFavorite,
                icon: Icon(
                  isFavorited ? Icons.favorite : Icons.favorite_border,
                  size: 30,
                  color: Colors.redAccent,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Tabs + date
          Row(
            children: [
              _buildTab("Description", isDescriptionSelected, () {
                setState(() {
                  isDescriptionSelected = true;
                });
              }),
              const SizedBox(width: 12),
              _buildTab("Details", !isDescriptionSelected, () {
                setState(() {
                  isDescriptionSelected = false;
                });
              }),
              const Spacer(),
              Text(
                widget.item.createdAt != null
                    ? timeago.format(widget.item.createdAt!)
                    : 'Unknown',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Contenu
          isDescriptionSelected
              ? Text(
                  widget.item.description,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    height: 1.4,
                  ),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 5),
                    TextRow(
                      label: "Condition: ",
                      value: widget.item.condition,
                    ),
                    TextRow(
                      label: "Category: ",
                      value: widget.item.categoryName ?? "Unknown",
                    ),
                    TextRow(
                      label: "Brand: ",
                      value: widget.item.brandName ?? "Unknown",
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildTab(String label, bool selected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: selected
                  ? Theme.of(context).colorScheme.onSurface
                  : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: selected
                ? Theme.of(context).colorScheme.onSurface
                : Colors.grey,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
